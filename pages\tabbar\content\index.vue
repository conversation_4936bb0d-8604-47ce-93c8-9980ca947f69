<template>
	<view class="container">
		<!-- 搜索框 -->
		<view class="search-bar">
			<view class="search-input-wrap">
				<tui-icon name="search" size="24" color="#999"></tui-icon>
				<input
					class="search-input"
					type="text"
					placeholder="输入想查找的内容"
					v-model="searchTitle"
					@confirm="onSearch"
					confirm-type="search"
				/>
				<tui-button
					width="120rpx"
					height="60rpx"
					:size="24"
					shape="circle"
					@click="onSearch"
				>
					搜索
				</tui-button>
			</view>
		</view>

		<!-- 内容列表 -->
		<view class="content-list">
			<view
				class="content-item"
				v-for="(item, index) in contentList"
				:key="item.pubId"
				@tap="goToDetail(item)"
			>
				<!-- 标题标签 -->
				<view class="item-header">
					<text class="title-tag" v-if="item.isFirst === '2000001'">置顶</text>
					<text class="item-title">{{ item.title }}</text>
				</view>

				<!-- 内容预览 -->
				<view class="item-content">
					<rich-text :nodes="getContentPreview(item.pubContent)"></rich-text>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<tui-loadmore v-if="loading"></tui-loadmore>
		<tui-nomore v-if="!pullUpOn && contentList.length > 0"></tui-nomore>

		<!-- 空状态 -->
		<view class="empty-state" v-if="!loading && contentList.length === 0">
			<text class="empty-text">暂无内容</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchTitle: '',
			contentList: [],
			loading: false,
			pullUpOn: true,
			pageIndex: 1,
			pageSize: 10
		}
	},
	onLoad() {
		this.loadContentList();
	},
	methods: {
		// 加载内容列表
		async loadContentList(refresh = false) {
			if (this.loading) return;

			this.loading = true;

			try {
				const params = {
					title: this.searchTitle,
					pubContentType: "2003001",
					page: refresh ? 1 : this.pageIndex,
					limit: this.pageSize
				};

				const response = await this.tui.request('/api/admin/biz-pub-content/list', 'POST', params);

				if (response.code === '0000') {
					const newData = response.data.rows || [];

					if (refresh) {
						this.contentList = newData;
						this.pageIndex = 1;
						this.pullUpOn = true;
					} else {
						this.contentList = this.contentList.concat(newData);
					}

					// 判断是否还有更多数据
					if (newData.length < this.pageSize) {
						this.pullUpOn = false;
					} else {
						this.pageIndex++;
					}
				} else {
					this.tui.toast(response.message || '加载失败');
				}
			} catch (error) {
				console.error('加载内容列表失败:', error);
				this.tui.toast('网络错误，请稍后重试');
			} finally {
				this.loading = false;
			}
		},

		// 执行搜索
		onSearch() {
			this.pageIndex = 1;
			this.contentList = [];
			this.pullUpOn = true;
			this.loadContentList(true);
		},

		// 获取内容预览
		getContentPreview(content) {
			if (!content) return '';
			// 移除HTML标签，只保留文本内容，限制长度
			const text = content.replace(/<[^>]*>/g, '');
			return text.length > 100 ? text.substring(0, 100) + '......' : text;
		},

		// 跳转到详情页
		goToDetail(item) {
			uni.navigateTo({
				url: `/pages/tabbar/content/detail?pubId=${item.pubId}`
			});
		}
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.loadContentList(true).then(() => {
			uni.stopPullDownRefresh();
			this.tui.toast('刷新成功');
		});
	},

	// 上拉加载更多
	onReachBottom() {
		if (!this.pullUpOn || this.loading) return;
		this.loadContentList();
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.search-bar {
	background-color: #fff;
	padding: 20rpx 30rpx;
	border-bottom: 1px solid #eee;
}

.search-input-wrap {
	display: flex;
	align-items: center;
	background-color: #f8f8f8;
	border-radius: 30rpx;
	padding: 0 20rpx;
	height: 70rpx;
}

.search-input {
	flex: 1;
	margin-left: 20rpx;
	margin-right: 20rpx;
	font-size: 28rpx;
	color: #333;
}

.content-list {
	padding: 20rpx 30rpx;
}

.content-item {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.item-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.title-tag {
	background-color: #ff4444;
	color: #fff;
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 6rpx;
	margin-right: 12rpx;
	flex-shrink: 0;
}

.item-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.4;
	flex: 1;
}

.item-content {
	color: #666;
	font-size: 28rpx;
	line-height: 1.5;
}

.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.empty-text {
	color: #999;
	font-size: 28rpx;
}
</style>