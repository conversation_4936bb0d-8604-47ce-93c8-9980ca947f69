<template>
	<view class="container">
		<view class="top-container">
			<image class="bg-img" src="https://scwl.kingvisus.com/static/images/mine_bg_3x.png"></image>
			<view @tap="logout" class="logout" hover-class="opcity" :hover-stay-time="150">
				<image class="logout-img" src="https://scwl.kingvisus.com/static/images/icon_out_3x.png" v-show="isLogin"></image>
				<text class="logout-txt" v-show="isLogin">退出</text>
			</view>
			<!-- 	<view v-show="!isLogin" class="user-wrapper">
				<navigator url="/pages/common/login/login" hover-class="opcity" :hover-stay-time="150" class="user">
					<image class="avatar-img" src="/static/images/my/mine_def_touxiang_3x.png"></image>
					<text class="user-info-mobile">请登录</text>
				</navigator>
			</view> -->
			<view class="user">
				<image class="avatar-img" src="https://scwl.kingvisus.com/static/images/mine_def_touxiang_3x.png"></image>
				<view class="user-info-mobile">
					<text>{{ username }}</text>
					<!-- <view class="edit-img" hover-class="opcity" :hover-stay-time="150" @tap="edit">
						<image src="/static/images/my/mine_icon_bianji_3x.png"></image>
					</view> -->
				</view>
			</view>
		</view>

		<view class="tui-profile-menu">
			<tui-list-cell padding="10rpx" hover>
				<view class="tui-flex-box" style="margin-left: 20rpx;margin-top: 20rpx;">
					<view class="tui-flex">
						<view class="tui-col-2" style="margin-top: -20rpx;margin-left: -10rpx;">
							<tui-icon name="edit" :size="36" color="#888"></tui-icon>
						</view>
						<view class="tui-col-3">
							<view>联系我们</view>
						</view>
						<view class="tui-col-7">

						</view>
						<view class="tui-col-3">
							<tui-button type="blue" @click="makePhoneCall" width="140rpx"
								height="50rpx">点击拨打</tui-button>
						</view>
					</view>
					<view class="tui-flex">
						<view class="tui-contact-info" style="margin-left: 80rpx;">
							<text>电话: ************</text>
						</view>
					</view>
				</view>
			</tui-list-cell>
			
			<tui-list-cell arrow @click="removeBindEvent" padding="30rpx" hover>
				<view class="tui-menu-item">
					<tui-icon name="edit" :size="36" color="#888"></tui-icon>
					<text class="tui-ml-20">解除绑定</text>
				</view>
			</tui-list-cell>
		</view>

		<view class="tui-applets__vip">
			<tui-icon name="applets" color="#07c160" :size="60" unit="rpx"></tui-icon>
			<text style="font-size: 16px;">砖瓦汇</text>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	import tui from '../../../common/httpRequest';
	
	export default {
		computed: mapState(['isLogin', 'mobile']),
		data() {
			return {
				username: '',
			};
		},
		onShow: function() {
			this.username = uni.getStorageSync('user');
		},
		methods: {
			logout() {
				this.tui.modal("提示", "确定退出登录？", true, (res) => {
					if (res) {
						uni.clearStorage();
						uni.reLaunch({
							url: '/pages/common/login/login'
						});
					}
				})
			},

			makePhoneCall() {
				// 拨打电话
				uni.makePhoneCall({
					phoneNumber: '************'
				});
			},
			removeBindEvent() {
				tui.request('/core/wxUser/del','post',{'userId':uni.getStorageSync('userId'),'openId':uni.getStorageSync('openId')},false,false,false).then((res) => {
					if(res.code === 200){
						uni.removeStorageSync('userId');
						uni.removeStorageSync('openId');
						uni.removeStorageSync('user');
						//跳转到登录页面
						uni.redirectTo({
							url: '/pages/common/login/index?type=1'
						})
					}
				}).catch((error)=>{
					
				})
			},
			onNavigationBarButtonTap(e) {
				if (e.index === 0) {
					// #ifdef APP-PLUS
					const subNVue = uni.getSubNVueById('share');
					subNVue.show('slide-in-bottom', 250);
					// #endif
				}
			},
		},
		
		
	};
</script>

<style>
	.container {
		position: relative;
		padding-bottom: 100rpx;
	}

	.top-container {
		height: 440rpx;
		position: relative;
		display: flex;
		flex-direction: column;
	}

	.bg-img {
		position: absolute;
		width: 100%;
		height: 440rpx;
	}

	.logout {
		width: 110rpx;
		height: 36rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		margin: 42rpx 0 24rpx 32rpx;
		position: relative;
		z-index: 2;
	}

	.logout-img {
		width: 36rpx;
		height: 36rpx;
		margin-right: 11rpx;
	}

	.logout-txt {
		font-size: 28rpx;
		color: #fefefe;
		line-height: 28rpx;
	}

	.user-wrapper {
		display: flex;
		justify-content: center;
		position: relative;
		z-index: 2;
	}

	.user {
		display: flex;
		flex-direction: column;
		justify-content: center;
		position: relative;
		z-index: 2;
	}

	.avatar-img {
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		align-self: center;
	}

	.user-info {
		display: flex;
		flex-direction: row;
		margin-top: 30rpx;
		align-items: center;
	}

	.user-info-mobile {
		margin-top: 30rpx;
		position: relative;
		font-size: 28rpx;
		color: #fefefe;
		line-height: 28rpx;
		align-self: center;
		padding: 0 50rpx;
	}

	.edit-img {
		position: absolute;
		width: 42rpx;
		height: 42rpx;
		right: 0;
		bottom: -4rpx;
	}

	.edit-img>image {
		width: 42rpx;
		height: 42rpx;
		padding-left: 25rpx;
	}

	.item {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
	}

	.tui-applets__vip {
		width: 100%;
		position: fixed;
		bottom: 20px;
		/* #ifdef H5 */
		bottom: 70px;
		padding-bottom: env(safe-area-inset-bottom);
		/* #endif */
		z-index: 10;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #586c94;
	}

	.tui-applets__vip text {
		padding-left: 10rpx;
	}

	.tui-profile-menu {
		padding: 20rpx;
	}

	.tui-menu-item {
		display: flex;
		align-items: center;
	}

	.tui-ml-20 {
		margin-left: 20rpx;
	}

	.tui-contact-info {
		font-size: 24rpx;
		color: #888;
		margin-top: 10rpx;
	}

	.tui-flex {
		display: -webkit-flex;
		display: flex;
	}

	.tui-col-12 {
		width: 100%;
	}

	.tui-col-11 {
		width: 91.66666667%;
	}

	.tui-col-10 {
		width: 83.33333333%;
	}

	.tui-col-9 {
		width: 75%;
	}

	.tui-col-8 {
		width: 66.66666667%;
	}

	.tui-col-7 {
		width: 58.33333333%;
	}

	.tui-col-6 {
		width: 50%;
	}

	.tui-col-5 {
		width: 41.66666667%;
	}

	.tui-col-4 {
		width: 33.33333333%;
	}

	.tui-col-3 {
		width: 25%;
	}

	.tui-col-2 {
		width: 16.66666667%;
	}

	.tui-col-1 {
		width: 8.33333333%;
	}
</style>